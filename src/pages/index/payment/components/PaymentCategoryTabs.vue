<script setup>
import { computed, ref } from 'vue'

// Props
const props = defineProps({
  categories: {
    type: Array,
    default: () => [
      { id: 'all', name: '全部费用', count: 8 },
      { id: 'rent', name: '租金', count: 4 },
      { id: 'property', name: '物业费', count: 2 },
      { id: 'utilities', name: '水电费', count: 1 },
      { id: 'deposit', name: '押金', count: 1 },
    ],
  },
  activeCategory: {
    type: String,
    default: 'all',
  },
})

// Emits
const emit = defineEmits(['categoryChange'])

// 响应式数据
const currentTab = ref(0)

// 计算属性
const tabIndex = computed({
  get() {
    return props.categories.findIndex(cat => cat.id === props.activeCategory)
  },
  set(index) {
    const category = props.categories[index]
    if (category) {
      emit('categoryChange', category.id)
    }
  },
})

// 方法
function handleTabClick(event) {
  const { index } = event.detail
  const category = props.categories[index]
  if (category) {
    uni.vibrateShort?.()
    emit('categoryChange', category.id)
  }
}
</script>

<template>
  <view class="bg-white relative">
    <wd-tabs
      v-model="tabIndex"
      swipeable
      class="hidden"
      @click="handleTabClick"
    >
      <wd-tab
        v-for="(category, index) in categories"
        :key="category.id"
        :title="category.name"
      >
        <view class="min-h-50">
          <slot :category="category" :index="index"></slot>
        </view>
      </wd-tab>
    </wd-tabs>
  </view>
</template>

<style lang="scss" scoped>
:deep(.wd-tabs__nav) {
  display: none;
}

:deep(.wd-tabs__content) {
  padding-top: 0;
  background: #f8fafc;
}
</style>
