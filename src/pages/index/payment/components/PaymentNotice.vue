<script setup>
import { ref } from 'vue'

// 响应式数据
const isExpanded = ref(false)

// 缴费说明内容
const noticeItems = [
  {
    icon: 'i-carbon-calendar',
    title: '租金缴纳',
    content: '租金按季度缴纳，每期需在到期前 10 天内支付，确保居住权益不受影响。'
  },
  {
    icon: 'i-carbon-flash',
    title: '水电费用',
    content: '水电费每月 5 日生成账单，可随时缴纳。建议及时关注用量，节约能源。'
  },
  {
    icon: 'i-carbon-warning',
    title: '逾期处理',
    content: '逾期未缴按日收取 0.05% 滞纳金，逾期 30 天将影响信用记录，请务必按时缴费。'
  },
  {
    icon: 'i-carbon-document',
    title: '收据获取',
    content: '缴费成功后可在"查看收据"中获取电子收据，如需纸质收据请联系前台办理。'
  }
]

// 方法
function toggleExpanded() {
  isExpanded.value = !isExpanded.value
  uni.vibrateShort?.()
}
</script>

<template>
  <view class="mx-4 mt-0">
    <wd-card class="rounded-4 overflow-hidden shadow-sm">
      <template #title>
        <view class="flex items-center justify-between cursor-pointer select-none" @click="toggleExpanded">
          <view class="flex items-center gap-3">
            <view class="i-carbon-information text-primary-500 text-xl"></view>
            <view>
              <view class="text-lg font-semibold text-gray-800 leading-tight">缴费说明</view>
              <view class="text-sm text-gray-500 mt-0.5">重要提醒和注意事项</view>
            </view>
          </view>
          <view class="transition-transform duration-300" :class="{ 'rotate-180': isExpanded }">
            <view class="i-carbon-chevron-down text-gray-400"></view>
          </view>
        </view>
      </template>

      <view
        class="overflow-hidden transition-all duration-300"
        :class="isExpanded ? 'max-h-250' : 'max-h-0'"
      >
        <view class="p-5 pb-4">
          <view
            v-for="(item, index) in noticeItems"
            :key="index"
            class="flex items-start gap-3 mb-5 last:mb-0"
          >
            <view class="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-blue-50 to-blue-100 rounded-2 flex items-center justify-center">
              <view :class="item.icon" class="text-base text-blue-600"></view>
            </view>
            <view class="flex-1">
              <view class="text-base font-semibold text-gray-800 mb-1 leading-tight">{{ item.title }}</view>
              <view class="text-sm text-gray-600 leading-relaxed">{{ item.content }}</view>
            </view>
          </view>
        </view>

        <!-- 联系方式 -->
        <view class="bg-gray-50 p-5 border-t border-gray-100">
          <view class="flex items-center text-base font-semibold text-gray-800 mb-3">
            <view class="i-carbon-phone text-primary-500 mr-2"></view>
            需要帮助？
          </view>
          <view class="space-y-2">
            <view class="flex items-center text-sm">
              <text class="text-gray-500 min-w-20">客服热线：</text>
              <text class="text-blue-600 font-medium underline cursor-pointer" @click="handleCall('************')">************</text>
            </view>
            <view class="flex items-center text-sm">
              <text class="text-gray-500 min-w-20">服务时间：</text>
              <text class="text-gray-800 font-medium">周一至周日 9:00-21:00</text>
            </view>
          </view>
        </view>
      </view>
    </wd-card>
  </view>
</template>

<script>
export default {
  methods: {
    handleCall(phone) {
      uni.makePhoneCall({
        phoneNumber: phone,
        success: () => {
          console.log('拨打电话成功')
        },
        fail: (err) => {
          console.error('拨打电话失败', err)
          uni.showToast({
            title: '拨打失败',
            icon: 'none'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.wd-card__header) {
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  cursor: pointer;
}

:deep(.wd-card__body) {
  padding: 0;
  background: white;
}
</style>
