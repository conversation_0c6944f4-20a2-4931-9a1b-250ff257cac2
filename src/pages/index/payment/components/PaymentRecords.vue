<script setup>
import { computed, ref } from 'vue'
import RecordItem from './RecordItem.vue'

// Props
const props = defineProps({
  records: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  hasMore: {
    type: Boolean,
    default: true,
  },
  activeCategory: {
    type: String,
    default: 'all',
  },
})

// Emits
const emit = defineEmits(['loadMore', 'viewReceipt', 'payNow'])

// 响应式数据
const loadingMore = ref(false)

// 计算属性
const filteredRecords = computed(() => {
  if (props.activeCategory === 'all') {
    return props.records
  }
  return props.records.filter(record => record.category === props.activeCategory)
})

const pendingRecords = computed(() => {
  return filteredRecords.value.filter(record => record.status === 'pending')
})

const paidRecords = computed(() => {
  return filteredRecords.value.filter(record => record.status === 'paid')
})

const hasRecords = computed(() => {
  return filteredRecords.value.length > 0
})

// 方法
async function handleLoadMore() {
  if (loadingMore.value || !props.hasMore)
    return

  try {
    loadingMore.value = true
    emit('loadMore')
  }
  finally {
    loadingMore.value = false
  }
}

function handleViewReceipt(record) {
  emit('viewReceipt', record)
}

function handlePayNow(record) {
  emit('payNow', record)
}
</script>

<template>
  <view class="bg-gray-50 min-h-100">
    <!-- 加载状态 -->
    <view v-if="loading && !hasRecords" class="flex flex-col items-center justify-center py-15">
      <wd-loading />
      <text class="mt-3 text-sm text-gray-500">
        加载中...
      </text>
    </view>

    <!-- 空状态 -->
    <view v-else-if="!hasRecords" class="flex flex-col items-center justify-center py-15">
      <view class="i-carbon-document text-6xl text-gray-300 mb-4"></view>
      <view class="text-base text-gray-700 font-medium mb-2">
        暂无缴费记录
      </view>
      <view class="text-sm text-gray-500 text-center leading-relaxed">
        您在该分类下还没有任何缴费记录
      </view>
    </view>

    <!-- 记录列表 -->
    <view v-else class="p-4">
      <!-- 待缴费用 -->
      <view v-if="pendingRecords.length > 0" class="mb-6">
        <view class="flex items-center justify-between mb-3 px-1">
          <view class="flex items-center text-base font-semibold text-gray-800">
            <view class="i-carbon-time text-orange-500 mr-2"></view>
            待缴费用
          </view>
          <view class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-3">{{ pendingRecords.length }} 笔</view>
        </view>
        <view class="space-y-2">
          <RecordItem
            v-for="record in pendingRecords"
            :key="record.id"
            :record="record"
            @view-receipt="handleViewReceipt"
            @pay-now="handlePayNow"
          />
        </view>
      </view>

      <!-- 已缴费用 -->
      <view v-if="paidRecords.length > 0" class="mb-6">
        <view class="flex items-center justify-between mb-3 px-1">
          <view class="flex items-center text-base font-semibold text-gray-800">
            <view class="i-carbon-checkmark-filled text-green-500 mr-2"></view>
            已缴费用
          </view>
          <view class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-3">{{ paidRecords.length }} 笔</view>
        </view>
        <view class="space-y-2">
          <RecordItem
            v-for="record in paidRecords"
            :key="record.id"
            :record="record"
            @view-receipt="handleViewReceipt"
            @pay-now="handlePayNow"
          />
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore" class="flex justify-center py-5">
        <wd-button
          v-if="!loadingMore"
          type="info"
          plain
          size="small"
          @click="handleLoadMore"
          class="rounded-5"
        >
          <view class="flex items-center">
            <view class="i-carbon-chevron-down mr-1"></view>
            加载更多
          </view>
        </wd-button>
        <view v-else class="flex items-center gap-2">
          <wd-loading size="16px" />
          <text class="text-sm text-gray-500">加载中...</text>
        </view>
      </view>

      <!-- 没有更多 -->
      <view v-else-if="filteredRecords.length > 0" class="flex items-center justify-center py-5 gap-3">
        <view class="flex-1 h-px bg-gray-200 max-w-15"></view>
        <text class="text-xs text-gray-400 whitespace-nowrap">没有更多记录了</text>
        <view class="flex-1 h-px bg-gray-200 max-w-15"></view>
      </view>
    </view>
  </view>
</template>


