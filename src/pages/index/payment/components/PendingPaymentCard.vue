<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  pendingPayment: {
    type: Object,
    default: () => ({
      amount: 2650.00,
      description: '2024年12月租金 + 物业费',
      dueDate: '2024-12-10',
      isOverdue: false
    })
  }
})

// Emits
const emit = defineEmits(['payNow'])

// 计算属性
const formattedAmount = computed(() => {
  return `¥${props.pendingPayment.amount.toFixed(2)}`
})

const dueDateText = computed(() => {
  const dueDate = new Date(props.pendingPayment.dueDate)
  const today = new Date()
  const diffTime = dueDate - today
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) {
    return `已逾期 ${Math.abs(diffDays)} 天`
  } else if (diffDays === 0) {
    return '今日到期'
  } else if (diffDays <= 3) {
    return `${diffDays} 天后到期`
  } else {
    return `截止日期：${props.pendingPayment.dueDate}`
  }
})

const isUrgent = computed(() => {
  const dueDate = new Date(props.pendingPayment.dueDate)
  const today = new Date()
  const diffTime = dueDate - today
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays <= 3
})

// 方法
function handlePayNow() {
  uni.vibrateShort?.()
  emit('payNow', props.pendingPayment)
}
</script>

<template>
  <wd-card class="mx-4 rounded-4 overflow-hidden shadow-sm">
    <template #title>
      <view class="flex items-center justify-between">
        <view class="flex items-center gap-3">
          <view class="i-carbon-wallet text-primary-500 text-xl"></view>
          <view>
            <view class="text-lg font-semibold text-gray-800 leading-tight">待缴费用</view>
            <view class="text-sm text-gray-500 mt-0.5">1 笔待缴费用</view>
          </view>
        </view>
        <view
          class="px-4 py-2 rounded-5 text-lg font-bold text-white shadow-md"
          :class="isUrgent ? 'bg-gradient-to-r from-orange-400 to-orange-600' : 'bg-gradient-to-r from-green-400 to-green-600'"
        >
          {{ formattedAmount }}
        </view>
      </view>
    </template>

    <view class="space-y-3">
      <view class="bg-white rounded-3 p-4 shadow-xs">
        <view class="flex justify-between items-center pb-3 border-b border-gray-100">
          <view class="text-sm text-gray-500">费用项目</view>
          <view class="text-sm text-gray-800 font-medium text-right">{{ pendingPayment.description }}</view>
        </view>
        <view class="flex justify-between items-center pt-3">
          <view class="text-sm text-gray-500">到期时间</view>
          <view
            class="text-sm font-medium text-right"
            :class="pendingPayment.isOverdue ? 'text-red-500' : isUrgent ? 'text-orange-500' : 'text-gray-800'"
          >
            {{ dueDateText }}
          </view>
        </view>
      </view>

      <view v-if="isUrgent || pendingPayment.isOverdue" class="flex items-center bg-orange-50 border border-orange-200 rounded-2 p-3">
        <view class="i-carbon-warning text-orange-500 mr-2"></view>
        <text class="text-sm text-orange-700 leading-relaxed">
          {{ pendingPayment.isOverdue ? '逾期将收取滞纳金，请尽快缴纳' : '即将到期，请及时缴纳' }}
        </text>
      </view>
    </view>

    <template #footer>
      <view>
        <wd-button
          type="primary"
          size="large"
          block
          @click="handlePayNow"
          class="rounded-3 h-12 text-base font-semibold shadow-lg"
        >
          <view class="flex items-center justify-center">
            <view class="i-carbon-payment mr-2"></view>
            立即缴纳
          </view>
        </wd-button>
      </view>
    </template>
  </wd-card>
</template>

<style lang="scss" scoped>
:deep(.wd-card__header) {
  padding: 20px 20px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

:deep(.wd-card__body) {
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

:deep(.wd-card__footer) {
  padding: 0 20px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}
</style>
