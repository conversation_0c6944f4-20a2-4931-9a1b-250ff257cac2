<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  record: {
    type: Object,
    required: true,
  },
})

// Emits
const emit = defineEmits(['viewReceipt', 'payNow'])

// 计算属性
const statusConfig = computed(() => {
  const configs = {
    pending: {
      text: '待缴费',
      class: 'status-pending',
      color: '#f59e0b',
      bgColor: '#fef3c7',
      icon: 'i-carbon-time',
    },
    paid: {
      text: '已缴费',
      class: 'status-paid',
      color: '#10b981',
      bgColor: '#d1fae5',
      icon: 'i-carbon-checkmark-filled',
    },
    overdue: {
      text: '已逾期',
      class: 'status-overdue',
      color: '#ef4444',
      bgColor: '#fee2e2',
      icon: 'i-carbon-warning',
    },
  }
  return configs[props.record.status] || configs.pending
})

const formattedAmount = computed(() => {
  return `¥${props.record.amount.toFixed(2)}`
})

const categoryName = computed(() => {
  const categoryMap = {
    rent: '租金',
    property: '物业费',
    utilities: '水电费',
    deposit: '押金',
  }
  return categoryMap[props.record.category] || '其他'
})

const paymentMethodText = computed(() => {
  const methodMap = {
    wechat: '微信支付',
    alipay: '支付宝',
    bank: '银行转账',
    cash: '现金支付',
  }
  return methodMap[props.record.paymentMethod] || '未知'
})

// 方法
function handleViewReceipt() {
  if (props.record.status === 'paid') {
    emit('viewReceipt', props.record)
  }
}

function handlePayNow() {
  if (props.record.status === 'pending') {
    emit('payNow', props.record)
  }
}

function formatDate(dateString) {
  if (!dateString)
    return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

function formatDateTime(dateString) {
  if (!dateString)
    return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}
</script>

<template>
  <wd-card
    class="rounded-3 overflow-hidden border transition-all duration-300 hover:shadow-md hover:transform hover:-translate-y-0.5"
    :class="record.status === 'pending' ? 'border-l-4 border-l-orange-400' : record.status === 'paid' ? 'border-l-4 border-l-green-400' : 'border-l-4 border-l-red-400'"
  >
    <view class="flex flex-col gap-3">
      <!-- 头部信息 -->
      <view class="flex justify-between items-start gap-3">
        <view class="flex-1">
          <view class="text-base font-semibold text-gray-800 leading-tight mb-1">{{ record.title }}</view>
          <view class="text-sm text-gray-500 leading-tight">{{ categoryName }} · {{ record.billNumber }}</view>
        </view>
        <view class="text-lg font-bold text-gray-800 text-right whitespace-nowrap">{{ formattedAmount }}</view>
      </view>

      <!-- 状态和时间信息 -->
      <view class="flex justify-between items-center gap-3">
        <view
          class="inline-flex items-center gap-1 px-2 py-1 rounded-3 text-xs font-medium"
          :style="{
            color: statusConfig.color,
            backgroundColor: statusConfig.bgColor
          }"
        >
          <view :class="statusConfig.icon" class="text-xs"></view>
          {{ statusConfig.text }}
        </view>

        <view class="text-xs text-right"
          :class="record.status === 'pending' ? 'text-orange-500 font-medium' : 'text-gray-500'"
        >
          <view v-if="record.status === 'pending'">
            截止：{{ formatDate(record.dueDate) }}
          </view>
          <view v-else-if="record.paidTime">
            {{ formatDateTime(record.paidTime) }}
          </view>
        </view>
      </view>

      <!-- 支付信息（已支付记录） -->
      <view v-if="record.status === 'paid' && record.paymentMethod" class="pt-2 border-t border-gray-100">
        <view class="flex items-center text-sm text-gray-500">
          <view class="i-carbon-wallet mr-1"></view>
          {{ paymentMethodText }}
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="flex justify-end gap-2 pt-2 border-t border-gray-100">
        <wd-button
          v-if="record.status === 'paid'"
          type="info"
          size="small"
          plain
          @click="handleViewReceipt"
          class="rounded-4"
        >
          <view class="flex items-center text-sm">
            <view class="i-carbon-document mr-1"></view>
            查看收据
          </view>
        </wd-button>

        <wd-button
          v-else-if="record.status === 'pending'"
          type="primary"
          size="small"
          @click="handlePayNow"
          class="rounded-4 shadow-sm"
        >
          <view class="flex items-center text-sm">
            <view class="i-carbon-payment mr-1"></view>
            立即缴费
          </view>
        </wd-button>

        <wd-button
          v-else-if="record.status === 'overdue'"
          type="error"
          size="small"
          @click="handlePayNow"
          class="rounded-4 shadow-sm"
        >
          <view class="flex items-center text-sm">
            <view class="i-carbon-warning mr-1"></view>
            逾期缴费
          </view>
        </wd-button>
      </view>
    </view>
  </wd-card>
</template>


